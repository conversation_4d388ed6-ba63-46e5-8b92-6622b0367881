import { AITool, AICategory, DbCategory, ToolFilters, AdminToolFilters, PaginationInfo, ContentStatus, FAQ, FAQFilters } from './types';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || '';

// API client for frontend
export class ApiClient {
  private async request<T>(endpoint: string, options?: RequestInit, returnFullResponse = false): Promise<T> {
    const url = `${API_BASE_URL}/api${endpoint}`;

    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options?.headers,
      },
      ...options,
    });

    if (!response.ok) {
      const error = await response.json().catch(() => ({ error: 'Unknown error' }));
      throw new Error(error.error || `HTTP ${response.status}`);
    }

    const data = await response.json();

    if (!data.success) {
      throw new Error(data.error || 'API request failed');
    }

    // Return full response for methods that need pagination info
    if (returnFullResponse) {
      return data as T;
    }

    return data.data;
  }

  // Tools API
  async getTools(filters?: ToolFilters): Promise<{
    data: AITool[];
    pagination: PaginationInfo;
  }> {
    const params = new URLSearchParams();

    if (filters?.category) params.append('category', filters.category);
    if (filters?.subcategory) params.append('subcategory', filters.subcategory);
    if (filters?.search) params.append('search', filters.search);
    if (filters?.pricing) params.append('pricing', filters.pricing);
    if (filters?.verified !== undefined) params.append('verified', filters.verified.toString());
    if (filters?.sortBy) params.append('sortBy', filters.sortBy);
    if (filters?.sortOrder) params.append('sortOrder', filters.sortOrder);
    if (filters?.page) params.append('page', filters.page.toString());
    if (filters?.limit) params.append('limit', filters.limit.toString());

    const queryString = params.toString();
    const endpoint = `/tools${queryString ? `?${queryString}` : ''}`;

    return this.request(endpoint, undefined, true);
  }

  async getToolById(id: string): Promise<AITool> {
    return this.request(`/tools/${id}`);
  }

  async createTool(toolData: Partial<AITool>, apiKey: string): Promise<AITool> {
    return this.request('/tools', {
      method: 'POST',
      headers: {
        'x-api-key': apiKey,
      },
      body: JSON.stringify(toolData),
    });
  }

  async updateTool(id: string, updates: Partial<AITool>, apiKey: string): Promise<AITool> {
    return this.request(`/tools/${id}`, {
      method: 'PUT',
      headers: {
        'x-api-key': apiKey,
      },
      body: JSON.stringify(updates),
    });
  }

  async deleteTool(id: string, apiKey: string): Promise<void> {
    return this.request(`/tools/${id}`, {
      method: 'DELETE',
      headers: {
        'x-api-key': apiKey,
      },
    });
  }

  // Admin-specific methods
  async getAdminTools(filters?: AdminToolFilters, apiKey?: string): Promise<{
    data: AITool[];
    pagination: PaginationInfo;
  }> {
    const params = new URLSearchParams();

    if (filters?.category) params.append('category', filters.category);
    if (filters?.subcategory) params.append('subcategory', filters.subcategory);
    if (filters?.page) params.append('page', filters.page.toString());
    if (filters?.limit) params.append('limit', filters.limit.toString());
    if (filters?.search) params.append('search', filters.search);
    if (filters?.pricing) params.append('pricing', filters.pricing);
    if (filters?.verified !== undefined) params.append('verified', filters.verified.toString());
    if (filters?.sortBy) params.append('sortBy', filters.sortBy);
    if (filters?.sortOrder) params.append('sortOrder', filters.sortOrder);

    // Admin-specific filters
    if (filters?.aiGenerationStatus) params.append('aiGenerationStatus', filters.aiGenerationStatus);
    if (filters?.contentStatus) params.append('contentStatus', filters.contentStatus);
    if (filters?.submissionType) params.append('submissionType', filters.submissionType);
    if (filters?.hasEditorialReview !== undefined) params.append('hasEditorialReview', filters.hasEditorialReview.toString());
    if (filters?.qualityScoreMin !== undefined) params.append('qualityScoreMin', filters.qualityScoreMin.toString());
    if (filters?.qualityScoreMax !== undefined) params.append('qualityScoreMax', filters.qualityScoreMax.toString());
    if (filters?.lastScrapedAfter) params.append('lastScrapedAfter', filters.lastScrapedAfter);
    if (filters?.lastScrapedBefore) params.append('lastScrapedBefore', filters.lastScrapedBefore);

    const endpoint = `/admin/tools${params.toString() ? `?${params.toString()}` : ''}`;

    return this.request(endpoint, {
      headers: apiKey ? { 'x-api-key': apiKey } : {},
    }, true);
  }

  async getAdminToolById(id: string, apiKey: string): Promise<AITool> {
    return this.request(`/admin/tools/${id}`, {
      method: 'GET',
      headers: {
        'x-api-key': apiKey,
      },
    });
  }

  async updateAdminTool(id: string, updates: Partial<AITool>, apiKey: string): Promise<AITool> {
    return this.request(`/admin/tools/${id}`, {
      method: 'PUT',
      headers: {
        'x-api-key': apiKey,
      },
      body: JSON.stringify(updates),
    });
  }

  async createAdminTool(toolData: any, apiKey: string): Promise<AITool> {
    return this.request('/admin/tools', {
      method: 'POST',
      headers: {
        'x-api-key': apiKey,
      },
      body: JSON.stringify(toolData),
    });
  }

  async deleteAdminTool(id: string, apiKey: string): Promise<void> {
    return this.request(`/admin/tools/${id}`, {
      method: 'DELETE',
      headers: {
        'x-api-key': apiKey,
      },
    });
  }

  // Bulk operations for admin tools
  async bulkUpdateToolStatus(toolIds: string[], status: ContentStatus, apiKey: string): Promise<any> {
    return this.request('/admin/tools/bulk', {
      method: 'POST',
      headers: {
        'x-api-key': apiKey,
      },
      body: JSON.stringify({
        action: 'updateStatus',
        toolIds,
        status,
      }),
    });
  }

  async bulkDeleteTools(toolIds: string[], apiKey: string): Promise<any> {
    return this.request('/admin/tools/bulk', {
      method: 'POST',
      headers: {
        'x-api-key': apiKey,
      },
      body: JSON.stringify({
        action: 'delete',
        toolIds,
      }),
    });
  }

  // Import/Export API methods
  async exportTools(format: 'json' | 'csv', filters?: {
    category?: string;
    status?: string;
    verified?: string;
    dateFrom?: string;
    dateTo?: string;
    fields?: string[];
  }, apiKey?: string): Promise<Blob> {
    const params = new URLSearchParams();
    params.append('format', format);

    if (filters?.category) params.append('category', filters.category);
    if (filters?.status) params.append('status', filters.status);
    if (filters?.verified) params.append('verified', filters.verified);
    if (filters?.dateFrom) params.append('dateFrom', filters.dateFrom);
    if (filters?.dateTo) params.append('dateTo', filters.dateTo);
    if (filters?.fields) params.append('fields', filters.fields.join(','));

    const response = await fetch(`/api/admin/tools/export?${params.toString()}`, {
      headers: apiKey ? { 'x-api-key': apiKey } : {},
    });

    if (!response.ok) {
      throw new Error('Export failed');
    }

    return response.blob();
  }

  async importTools(file: File, options?: {
    preview?: boolean;
    duplicateStrategy?: 'skip' | 'update' | 'error';
  }, apiKey?: string): Promise<any> {
    const formData = new FormData();
    formData.append('file', file);

    if (options?.preview) {
      formData.append('preview', 'true');
    }

    if (options?.duplicateStrategy) {
      formData.append('duplicateStrategy', options.duplicateStrategy);
    }

    const response = await fetch(`/api/admin/tools/import`, {
      method: 'POST',
      headers: apiKey ? { 'x-api-key': apiKey } : {},
      body: formData,
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Import failed');
    }

    return response.json();
  }

  // Categories API
  async getCategories(): Promise<AICategory[]> {
    return this.request('/categories');
  }

  // Admin Categories API
  async getAdminCategories(apiKey: string): Promise<DbCategory[]> {
    return this.request('/admin/categories', {
      headers: {
        'x-api-key': apiKey,
      },
    });
  }

  async createAdminCategory(categoryData: Partial<DbCategory>, apiKey: string): Promise<DbCategory> {
    return this.request('/admin/categories', {
      method: 'POST',
      headers: {
        'x-api-key': apiKey,
      },
      body: JSON.stringify(categoryData),
    });
  }

  async updateAdminCategory(id: string, updates: Partial<DbCategory>, apiKey: string): Promise<DbCategory> {
    return this.request(`/admin/categories/${id}`, {
      method: 'PUT',
      headers: {
        'x-api-key': apiKey,
      },
      body: JSON.stringify(updates),
    });
  }

  async deleteAdminCategory(id: string, apiKey: string): Promise<void> {
    return this.request(`/admin/categories/${id}`, {
      method: 'DELETE',
      headers: {
        'x-api-key': apiKey,
      },
    });
  }

  async getAdminCategory(id: string, apiKey: string): Promise<DbCategory> {
    return this.request(`/admin/categories/${id}`, {
      headers: {
        'x-api-key': apiKey,
      },
    });
  }

  // Search API
  async searchTools(query: string, limit?: number): Promise<AITool[]> {
    const params = new URLSearchParams({ search: query });
    if (limit) params.append('limit', limit.toString());

    const result = await this.request<{
      data: AITool[];
      pagination: PaginationInfo;
    }>(`/tools?${params.toString()}`, undefined, true);

    return result.data;
  }

  // Tool submission API
  async submitTool(submission: {
    name: string;
    url: string;
    description: string;
    category: string;
    subcategory?: string;
    submitterName?: string;
    submitterEmail: string;
    logoUrl?: string;
    tags?: string[];
    pricingType?: string;
  }): Promise<void> {
    return this.request('/submissions', {
      method: 'POST',
      body: JSON.stringify(submission),
    });
  }

  // Content generation API (admin only)
  async generateContent(data: {
    url: string;
    scrapedData: any;
    pricingData?: any;
    faqData?: any;
  }, apiKey: string): Promise<any> {
    return this.request('/generate-content', {
      method: 'POST',
      headers: {
        'x-api-key': apiKey,
      },
      body: JSON.stringify(data),
    });
  }

  // Web scraping API (admin only)
  async scrapeUrl(url: string, options?: any, apiKey?: string): Promise<any> {
    return this.request('/scrape', {
      method: 'POST',
      headers: apiKey ? { 'x-api-key': apiKey } : {},
      body: JSON.stringify({ url, options }),
    });
  }

  // FAQ API methods (JSONB-based)

  // Get FAQs for a specific tool (public)
  async getToolFAQs(toolId: string, filters?: Partial<FAQFilters>): Promise<FAQ[]> {
    const params = new URLSearchParams();
    if (filters?.category) params.append('category', filters.category);
    if (filters?.isFeatured !== undefined) params.append('featured', filters.isFeatured.toString());
    if (filters?.search) params.append('search', filters.search);
    if (filters?.limit) params.append('limit', filters.limit.toString());
    if (filters?.sortBy) params.append('sortBy', filters.sortBy);
    if (filters?.sortOrder) params.append('sortOrder', filters.sortOrder);

    const queryString = params.toString();
    const endpoint = `/tools/${toolId}/faqs${queryString ? `?${queryString}` : ''}`;

    return this.request(endpoint);
  }

  // Admin FAQ API methods

  // Update all FAQs for a tool (admin only)
  async updateToolFAQs(toolId: string, faqs: FAQ[], apiKey: string): Promise<FAQ[]> {
    return this.request('/admin/faqs', {
      method: 'PUT',
      headers: {
        'x-api-key': apiKey,
      },
      body: JSON.stringify({ toolId, faqs }),
    });
  }

  // Add a new FAQ to a tool (admin only)
  async addFAQToTool(toolId: string, faq: Partial<FAQ>, apiKey: string): Promise<FAQ[]> {
    return this.request('/admin/faqs', {
      method: 'POST',
      headers: {
        'x-api-key': apiKey,
      },
      body: JSON.stringify({ toolId, faq }),
    });
  }

  // Update a specific FAQ in a tool (admin only)
  async updateFAQInTool(toolId: string, faqId: string, updates: Partial<FAQ>, apiKey: string): Promise<FAQ[]> {
    return this.request(`/admin/faqs/${faqId}`, {
      method: 'PUT',
      headers: {
        'x-api-key': apiKey,
      },
      body: JSON.stringify({ toolId, updates }),
    });
  }

  // Remove a FAQ from a tool (admin only)
  async removeFAQFromTool(toolId: string, faqId: string, apiKey: string): Promise<FAQ[]> {
    return this.request(`/admin/faqs/${faqId}`, {
      method: 'DELETE',
      headers: {
        'x-api-key': apiKey,
      },
      body: JSON.stringify({ toolId }),
    });
  }
}

// Export singleton instance
export const apiClient = new ApiClient();

// Convenience functions for common operations
export async function getToolsForCategory(categoryId: string, limit?: number): Promise<AITool[]> {
  const result = await apiClient.getTools({ 
    category: categoryId, 
    limit: limit || 20,
    sortBy: 'created_at',
    sortOrder: 'desc'
  });
  return result.data;
}

export async function getFeaturedTools(limit: number = 10): Promise<AITool[]> {
  const result = await apiClient.getTools({ 
    verified: true,
    limit,
    sortBy: 'created_at',
    sortOrder: 'desc'
  });
  return result.data;
}

export async function getToolsByPricing(pricingType: 'free' | 'freemium' | 'paid' | 'open source' | 'subscription', limit?: number): Promise<AITool[]> {
  const result = await apiClient.getTools({
    pricing: pricingType,
    limit: limit || 20,
    sortBy: 'created_at',
    sortOrder: 'desc'
  });
  return result.data;
}

// Error handling utility
export function isApiError(error: unknown): error is Error {
  return error instanceof Error;
}

export function getErrorMessage(error: unknown): string {
  if (isApiError(error)) {
    return error.message;
  }
  return 'An unexpected error occurred';
}
