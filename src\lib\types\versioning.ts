/**
 * Tool Versioning System Types
 * Comprehensive type definitions for version control functionality
 */

import { AITool, DbTool } from './index';

// Core Version Types
export interface ToolVersion {
  id: string;
  toolId: string;
  versionNumber: number;
  versionData: AITool; // Complete tool snapshot
  changeSummary?: string;
  createdBy: string;
  createdAt: string;
  
  // Metadata
  isCurrent: boolean;
  changeType: VersionChangeType;
  changeSource: VersionChangeSource;
  
  // Version control
  parentVersionId?: string;
  rollbackReason?: string;
}

// Database Version Interface (snake_case)
export interface DbToolVersion {
  id: string;
  tool_id: string;
  version_number: number;
  version_data: any; // JSONB - complete tool snapshot
  change_summary?: string;
  created_by: string;
  created_at: string;
  
  // Metadata
  is_current: boolean;
  change_type: VersionChangeType;
  change_source: VersionChangeSource;
  
  // Version control
  parent_version_id?: string;
  rollback_reason?: string;
}

// Version Change Types
export type VersionChangeType = 'create' | 'update' | 'rollback' | 'bulk_update';
export type VersionChangeSource = 'admin_panel' | 'api' | 'bulk_import' | 'automation';

// Audit Log Types
export interface VersionAuditLog {
  id: string;
  toolId: string;
  versionId?: string;
  action: VersionAuditAction;
  performedBy: string;
  performedAt: string;
  
  // Action details
  actionDetails?: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
  
  // Rollback specific
  targetVersionNumber?: number;
  rollbackReason?: string;
  
  // System metadata
  sessionId?: string;
  requestId?: string;
}

// Database Audit Log Interface
export interface DbVersionAuditLog {
  id: string;
  tool_id: string;
  version_id?: string;
  action: VersionAuditAction;
  performed_by: string;
  performed_at: string;
  
  // Action details
  action_details?: any; // JSONB
  ip_address?: string;
  user_agent?: string;
  
  // Rollback specific
  target_version_number?: number;
  rollback_reason?: string;
  
  // System metadata
  session_id?: string;
  request_id?: string;
}

export type VersionAuditAction = 'create_version' | 'rollback' | 'compare' | 'view' | 'delete_version';

// Version Comparison Types
export interface VersionComparison {
  id: string;
  toolId: string;
  fromVersionId: string;
  toVersionId: string;
  comparisonData: VersionDiff;
  createdAt: string;
  expiresAt: string;
}

export interface DbVersionComparison {
  id: string;
  tool_id: string;
  from_version_id: string;
  to_version_id: string;
  comparison_data: any; // JSONB
  created_at: string;
  expires_at: string;
}

// Version Diff Structure
export interface VersionDiff {
  summary: DiffSummary;
  changes: FieldChange[];
  metadata: DiffMetadata;
}

export interface DiffSummary {
  totalChanges: number;
  addedFields: number;
  modifiedFields: number;
  removedFields: number;
  significantChanges: string[]; // List of important field changes
}

export interface FieldChange {
  field: string;
  type: 'added' | 'modified' | 'removed';
  oldValue?: any;
  newValue?: any;
  path: string; // Dot notation path to the field
  significance: 'low' | 'medium' | 'high'; // Impact level
}

export interface DiffMetadata {
  comparedAt: string;
  fromVersion: number;
  toVersion: number;
  algorithm: string; // Diff algorithm used
  processingTime: number; // Time taken to compute diff (ms)
}

// Version Management Operations
export interface CreateVersionRequest {
  toolId: string;
  changeSummary?: string;
  changeType?: VersionChangeType;
  changeSource?: VersionChangeSource;
  createdBy: string;
  
  // Context
  sessionId?: string;
  requestId?: string;
  ipAddress?: string;
  userAgent?: string;
}

export interface RollbackRequest {
  toolId: string;
  targetVersionNumber: number;
  reason: string;
  performedBy: string;
  
  // Context
  sessionId?: string;
  requestId?: string;
  ipAddress?: string;
  userAgent?: string;
}

export interface CompareVersionsRequest {
  toolId: string;
  fromVersionNumber: number;
  toVersionNumber: number;
  includeMetadata?: boolean;
}

// Version List and Pagination
export interface VersionListRequest {
  toolId: string;
  page?: number;
  limit?: number;
  includeData?: boolean; // Whether to include full version data
  changeType?: VersionChangeType;
  createdBy?: string;
  dateFrom?: string;
  dateTo?: string;
}

export interface VersionListResponse {
  versions: ToolVersion[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  metadata: {
    currentVersion: number;
    totalVersions: number;
    oldestVersion: number;
    newestVersion: number;
  };
}

// Version Statistics
export interface VersionStatistics {
  toolId: string;
  totalVersions: number;
  currentVersion: number;
  
  // Change frequency
  versionsThisWeek: number;
  versionsThisMonth: number;
  averageChangesPerWeek: number;
  
  // Change types breakdown
  changeTypeBreakdown: Record<VersionChangeType, number>;
  changeSourceBreakdown: Record<VersionChangeSource, number>;
  
  // User activity
  topContributors: Array<{
    userId: string;
    versionCount: number;
    lastChange: string;
  }>;
  
  // Recent activity
  recentVersions: ToolVersion[];
  lastRollback?: {
    versionNumber: number;
    performedAt: string;
    performedBy: string;
    reason: string;
  };
}

// Rollback Validation
export interface RollbackValidation {
  isValid: boolean;
  canRollback: boolean;
  warnings: string[];
  errors: string[];
  
  // Impact analysis
  impactAnalysis: {
    affectedFields: string[];
    dataLossRisk: 'none' | 'low' | 'medium' | 'high';
    dependencyImpact: string[];
    estimatedDowntime: number; // seconds
  };
  
  // Rollback preview
  preview?: {
    currentData: Partial<AITool>;
    targetData: Partial<AITool>;
    changes: FieldChange[];
  };
}

// Version Management Service Response Types
export interface VersionOperationResult {
  success: boolean;
  data?: any;
  error?: string;
  warnings?: string[];
  
  // Operation metadata
  operationId: string;
  timestamp: string;
  performedBy: string;
  
  // Audit trail
  auditLogId?: string;
  versionId?: string;
}

// Bulk Version Operations
export interface BulkVersionOperation {
  operation: 'create_versions' | 'rollback_multiple' | 'cleanup_old_versions';
  toolIds: string[];
  parameters: Record<string, any>;
  performedBy: string;
  
  // Context
  sessionId?: string;
  requestId?: string;
}

export interface BulkVersionResult {
  totalProcessed: number;
  successful: number;
  failed: number;
  results: Array<{
    toolId: string;
    success: boolean;
    error?: string;
    versionId?: string;
  }>;
  
  // Summary
  operationSummary: {
    startTime: string;
    endTime: string;
    duration: number; // milliseconds
    operationId: string;
  };
}

// Version Cleanup Configuration
export interface VersionCleanupConfig {
  retentionDays: number; // How long to keep versions
  maxVersionsPerTool: number; // Maximum versions to keep per tool
  preserveCurrentVersion: boolean;
  preserveRollbackVersions: boolean; // Keep versions that were rolled back to
  cleanupFrequency: 'daily' | 'weekly' | 'monthly';
  
  // Advanced options
  compressOldVersions: boolean; // Compress version data for old versions
  archiveThreshold: number; // Days after which to archive versions
}

// Export all types
export type {
  ToolVersion,
  DbToolVersion,
  VersionAuditLog,
  DbVersionAuditLog,
  VersionComparison,
  DbVersionComparison,
  VersionDiff,
  DiffSummary,
  FieldChange,
  DiffMetadata,
  CreateVersionRequest,
  RollbackRequest,
  CompareVersionsRequest,
  VersionListRequest,
  VersionListResponse,
  VersionStatistics,
  RollbackValidation,
  VersionOperationResult,
  BulkVersionOperation,
  BulkVersionResult,
  VersionCleanupConfig
};
