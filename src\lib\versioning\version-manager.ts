/**
 * Tool Version Manager
 * Core service for managing tool versions, rollbacks, and audit logging
 */

import { supabaseAdmin } from '@/lib/supabase';
import { AITool, DbTool } from '@/lib/types';
import {
  ToolVersion,
  DbToolVersion,
  CreateVersionRequest,
  RollbackRequest,
  VersionOperationResult,
  VersionListRequest,
  VersionListResponse,
  VersionStatistics,
  RollbackValidation,
  VersionAuditAction,
  VersionChangeType,
  VersionChangeSource
} from '@/lib/types/versioning';
import { transformDbToolToAITool, transformAIToolToDbTool } from '@/lib/data-transformers';
import { VersionAuditLogger } from './audit-logger';
import { VersionComparator } from './version-comparator';

export class VersionManager {
  private auditLogger: VersionAuditLogger;
  private comparator: VersionComparator;

  constructor() {
    this.auditLogger = new VersionAuditLogger();
    this.comparator = new VersionComparator();
  }

  /**
   * Create a new version of a tool
   */
  async createVersion(request: CreateVersionRequest): Promise<VersionOperationResult> {
    if (!supabaseAdmin) {
      throw new Error('Admin operations require SUPABASE_SERVICE_ROLE_KEY');
    }

    const operationId = this.generateOperationId();
    
    try {
      // Get current tool data
      const { data: currentTool, error: toolError } = await supabaseAdmin
        .from('tools')
        .select('*')
        .eq('id', request.toolId)
        .single();

      if (toolError || !currentTool) {
        throw new Error(`Tool not found: ${request.toolId}`);
      }

      // Transform to AITool format for version storage
      const toolData = transformDbToolToAITool(currentTool);

      // Create version record
      const versionData: Omit<DbToolVersion, 'id' | 'version_number' | 'created_at'> = {
        tool_id: request.toolId,
        version_data: toolData,
        change_summary: request.changeSummary || 'Tool updated',
        created_by: request.createdBy,
        is_current: true,
        change_type: request.changeType || 'update',
        change_source: request.changeSource || 'admin_panel',
        parent_version_id: await this.getCurrentVersionId(request.toolId),
        rollback_reason: null
      };

      const { data: newVersion, error: versionError } = await supabaseAdmin
        .from('tool_versions')
        .insert([versionData])
        .select()
        .single();

      if (versionError) {
        throw new Error(`Failed to create version: ${versionError.message}`);
      }

      // Update tool's current version reference
      await this.updateToolVersionReference(request.toolId, newVersion.id);

      // Log the action
      await this.auditLogger.logAction({
        toolId: request.toolId,
        versionId: newVersion.id,
        action: 'create_version',
        performedBy: request.createdBy,
        actionDetails: {
          changeSummary: request.changeSummary,
          changeType: request.changeType,
          changeSource: request.changeSource
        },
        sessionId: request.sessionId,
        requestId: request.requestId,
        ipAddress: request.ipAddress,
        userAgent: request.userAgent
      });

      return {
        success: true,
        data: this.transformDbVersionToVersion(newVersion),
        operationId,
        timestamp: new Date().toISOString(),
        performedBy: request.createdBy,
        versionId: newVersion.id
      };

    } catch (error) {
      console.error('Version creation failed:', error);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        operationId,
        timestamp: new Date().toISOString(),
        performedBy: request.createdBy
      };
    }
  }

  /**
   * Rollback tool to a specific version
   */
  async rollbackToVersion(request: RollbackRequest): Promise<VersionOperationResult> {
    if (!supabaseAdmin) {
      throw new Error('Admin operations require SUPABASE_SERVICE_ROLE_KEY');
    }

    const operationId = this.generateOperationId();

    try {
      // Validate rollback request
      const validation = await this.validateRollback(request);
      if (!validation.canRollback) {
        throw new Error(`Rollback validation failed: ${validation.errors.join(', ')}`);
      }

      // Get target version data
      const { data: targetVersion, error: versionError } = await supabaseAdmin
        .from('tool_versions')
        .select('*')
        .eq('tool_id', request.toolId)
        .eq('version_number', request.targetVersionNumber)
        .single();

      if (versionError || !targetVersion) {
        throw new Error(`Target version not found: ${request.targetVersionNumber}`);
      }

      // Begin transaction for rollback
      const { data: rolledBackTool, error: rollbackError } = await supabaseAdmin.rpc(
        'rollback_tool_to_version',
        {
          p_tool_id: request.toolId,
          p_target_version_data: targetVersion.version_data,
          p_rollback_reason: request.reason,
          p_performed_by: request.performedBy
        }
      );

      if (rollbackError) {
        throw new Error(`Rollback failed: ${rollbackError.message}`);
      }

      // Create rollback version entry
      const rollbackVersionData: Omit<DbToolVersion, 'id' | 'version_number' | 'created_at'> = {
        tool_id: request.toolId,
        version_data: targetVersion.version_data,
        change_summary: `Rolled back to version ${request.targetVersionNumber}`,
        created_by: request.performedBy,
        is_current: true,
        change_type: 'rollback',
        change_source: 'admin_panel',
        parent_version_id: await this.getCurrentVersionId(request.toolId),
        rollback_reason: request.reason
      };

      const { data: rollbackVersion, error: rollbackVersionError } = await supabaseAdmin
        .from('tool_versions')
        .insert([rollbackVersionData])
        .select()
        .single();

      if (rollbackVersionError) {
        throw new Error(`Failed to create rollback version: ${rollbackVersionError.message}`);
      }

      // Update tool's current version reference
      await this.updateToolVersionReference(request.toolId, rollbackVersion.id);

      // Log the rollback action
      await this.auditLogger.logAction({
        toolId: request.toolId,
        versionId: rollbackVersion.id,
        action: 'rollback',
        performedBy: request.performedBy,
        targetVersionNumber: request.targetVersionNumber,
        rollbackReason: request.reason,
        actionDetails: {
          targetVersion: request.targetVersionNumber,
          reason: request.reason
        },
        sessionId: request.sessionId,
        requestId: request.requestId,
        ipAddress: request.ipAddress,
        userAgent: request.userAgent
      });

      return {
        success: true,
        data: {
          rolledBackVersion: this.transformDbVersionToVersion(rollbackVersion),
          targetVersion: this.transformDbVersionToVersion(targetVersion)
        },
        operationId,
        timestamp: new Date().toISOString(),
        performedBy: request.performedBy,
        versionId: rollbackVersion.id
      };

    } catch (error) {
      console.error('Rollback failed:', error);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        operationId,
        timestamp: new Date().toISOString(),
        performedBy: request.performedBy
      };
    }
  }

  /**
   * Get version history for a tool
   */
  async getVersionHistory(request: VersionListRequest): Promise<VersionListResponse> {
    if (!supabaseAdmin) {
      throw new Error('Admin operations require SUPABASE_SERVICE_ROLE_KEY');
    }

    const page = request.page || 1;
    const limit = request.limit || 20;
    const offset = (page - 1) * limit;

    let query = supabaseAdmin
      .from('tool_versions')
      .select('*', { count: 'exact' })
      .eq('tool_id', request.toolId)
      .order('version_number', { ascending: false });

    // Apply filters
    if (request.changeType) {
      query = query.eq('change_type', request.changeType);
    }
    if (request.createdBy) {
      query = query.eq('created_by', request.createdBy);
    }
    if (request.dateFrom) {
      query = query.gte('created_at', request.dateFrom);
    }
    if (request.dateTo) {
      query = query.lte('created_at', request.dateTo);
    }

    // Apply pagination
    query = query.range(offset, offset + limit - 1);

    const { data: versions, error, count } = await query;

    if (error) {
      throw new Error(`Failed to fetch version history: ${error.message}`);
    }

    const transformedVersions = versions?.map(v => this.transformDbVersionToVersion(v)) || [];

    // Get metadata
    const { data: currentVersionData } = await supabaseAdmin
      .from('tool_versions')
      .select('version_number')
      .eq('tool_id', request.toolId)
      .eq('is_current', true)
      .single();

    const { data: versionStats } = await supabaseAdmin
      .from('tool_versions')
      .select('version_number')
      .eq('tool_id', request.toolId)
      .order('version_number', { ascending: true });

    const totalVersions = count || 0;
    const currentVersion = currentVersionData?.version_number || 0;
    const oldestVersion = versionStats?.[0]?.version_number || 0;
    const newestVersion = versionStats?.[versionStats.length - 1]?.version_number || 0;

    return {
      versions: transformedVersions,
      pagination: {
        page,
        limit,
        total: totalVersions,
        totalPages: Math.ceil(totalVersions / limit)
      },
      metadata: {
        currentVersion,
        totalVersions,
        oldestVersion,
        newestVersion
      }
    };
  }

  /**
   * Get version statistics for a tool
   */
  async getVersionStatistics(toolId: string): Promise<VersionStatistics> {
    if (!supabaseAdmin) {
      throw new Error('Admin operations require SUPABASE_SERVICE_ROLE_KEY');
    }

    // Get basic version counts
    const { data: versions, error } = await supabaseAdmin
      .from('tool_versions')
      .select('*')
      .eq('tool_id', toolId)
      .order('created_at', { ascending: false });

    if (error) {
      throw new Error(`Failed to fetch version statistics: ${error.message}`);
    }

    const totalVersions = versions?.length || 0;
    const currentVersion = versions?.find(v => v.is_current)?.version_number || 0;

    // Calculate time-based statistics
    const now = new Date();
    const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

    const versionsThisWeek = versions?.filter(v =>
      new Date(v.created_at) >= oneWeekAgo
    ).length || 0;

    const versionsThisMonth = versions?.filter(v =>
      new Date(v.created_at) >= oneMonthAgo
    ).length || 0;

    // Calculate change type breakdown
    const changeTypeBreakdown = versions?.reduce((acc, v) => {
      acc[v.change_type] = (acc[v.change_type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>) || {};

    // Calculate change source breakdown
    const changeSourceBreakdown = versions?.reduce((acc, v) => {
      acc[v.change_source] = (acc[v.change_source] || 0) + 1;
      return acc;
    }, {} as Record<string, number>) || {};

    // Get top contributors
    const contributorCounts = versions?.reduce((acc, v) => {
      acc[v.created_by] = (acc[v.created_by] || 0) + 1;
      return acc;
    }, {} as Record<string, number>) || {};

    const topContributors = Object.entries(contributorCounts)
      .map(([userId, count]) => ({
        userId,
        versionCount: count,
        lastChange: versions?.find(v => v.created_by === userId)?.created_at || ''
      }))
      .sort((a, b) => b.versionCount - a.versionCount)
      .slice(0, 5);

    // Get recent versions
    const recentVersions = versions?.slice(0, 5).map(v => this.transformDbVersionToVersion(v)) || [];

    // Find last rollback
    const lastRollback = versions?.find(v => v.change_type === 'rollback');

    return {
      toolId,
      totalVersions,
      currentVersion,
      versionsThisWeek,
      versionsThisMonth,
      averageChangesPerWeek: totalVersions > 0 ? versionsThisWeek : 0,
      changeTypeBreakdown: changeTypeBreakdown as Record<VersionChangeType, number>,
      changeSourceBreakdown: changeSourceBreakdown as Record<VersionChangeSource, number>,
      topContributors,
      recentVersions,
      lastRollback: lastRollback ? {
        versionNumber: lastRollback.version_number,
        performedAt: lastRollback.created_at,
        performedBy: lastRollback.created_by,
        reason: lastRollback.rollback_reason || 'No reason provided'
      } : undefined
    };
  }

  /**
   * Validate if rollback is possible
   */
  async validateRollback(request: RollbackRequest): Promise<RollbackValidation> {
    const warnings: string[] = [];
    const errors: string[] = [];

    try {
      // Check if target version exists
      const { data: targetVersion, error } = await supabaseAdmin
        .from('tool_versions')
        .select('*')
        .eq('tool_id', request.toolId)
        .eq('version_number', request.targetVersionNumber)
        .single();

      if (error || !targetVersion) {
        errors.push(`Target version ${request.targetVersionNumber} not found`);
      }

      // Check if tool exists
      const { data: tool, error: toolError } = await supabaseAdmin
        .from('tools')
        .select('id, name')
        .eq('id', request.toolId)
        .single();

      if (toolError || !tool) {
        errors.push('Tool not found');
      }

      // Check if trying to rollback to current version
      const { data: currentVersion } = await supabaseAdmin
        .from('tool_versions')
        .select('version_number')
        .eq('tool_id', request.toolId)
        .eq('is_current', true)
        .single();

      if (currentVersion?.version_number === request.targetVersionNumber) {
        errors.push('Cannot rollback to current version');
      }

      // Add warnings for potential data loss
      if (targetVersion && currentVersion) {
        if (targetVersion.version_number < currentVersion.version_number) {
          warnings.push('Rolling back will lose changes made in newer versions');
        }
      }

      const canRollback = errors.length === 0;

      return {
        isValid: canRollback,
        canRollback,
        warnings,
        errors,
        impactAnalysis: {
          affectedFields: [], // Would be populated by comparison
          dataLossRisk: warnings.length > 0 ? 'medium' : 'low',
          dependencyImpact: [],
          estimatedDowntime: 5 // seconds
        }
      };

    } catch (error) {
      errors.push(error instanceof Error ? error.message : 'Validation failed');
      
      return {
        isValid: false,
        canRollback: false,
        warnings,
        errors,
        impactAnalysis: {
          affectedFields: [],
          dataLossRisk: 'high',
          dependencyImpact: [],
          estimatedDowntime: 0
        }
      };
    }
  }

  // Helper methods
  private async getCurrentVersionId(toolId: string): Promise<string | undefined> {
    const { data } = await supabaseAdmin
      .from('tool_versions')
      .select('id')
      .eq('tool_id', toolId)
      .eq('is_current', true)
      .single();

    return data?.id;
  }

  private async updateToolVersionReference(toolId: string, versionId: string): Promise<void> {
    await supabaseAdmin
      .from('tools')
      .update({ 
        current_version_id: versionId,
        updated_at: new Date().toISOString()
      })
      .eq('id', toolId);
  }

  private transformDbVersionToVersion(dbVersion: DbToolVersion): ToolVersion {
    return {
      id: dbVersion.id,
      toolId: dbVersion.tool_id,
      versionNumber: dbVersion.version_number,
      versionData: dbVersion.version_data,
      changeSummary: dbVersion.change_summary,
      createdBy: dbVersion.created_by,
      createdAt: dbVersion.created_at,
      isCurrent: dbVersion.is_current,
      changeType: dbVersion.change_type,
      changeSource: dbVersion.change_source,
      parentVersionId: dbVersion.parent_version_id,
      rollbackReason: dbVersion.rollback_reason
    };
  }

  private generateOperationId(): string {
    return `ver_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
