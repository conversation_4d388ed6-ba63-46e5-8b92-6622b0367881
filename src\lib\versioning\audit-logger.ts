/**
 * Version Audit Logger
 * Handles comprehensive audit logging for all versioning operations
 */

import { supabaseAdmin } from '@/lib/supabase';
import {
  VersionAuditLog,
  DbVersionAuditLog,
  VersionAuditAction
} from '@/lib/types/versioning';

export interface AuditLogRequest {
  toolId: string;
  versionId?: string;
  action: VersionAuditAction;
  performedBy: string;
  
  // Action details
  actionDetails?: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
  
  // Rollback specific
  targetVersionNumber?: number;
  rollbackReason?: string;
  
  // System metadata
  sessionId?: string;
  requestId?: string;
}

export class VersionAuditLogger {
  
  /**
   * Log a versioning action
   */
  async logAction(request: AuditLogRequest): Promise<string> {
    if (!supabaseAdmin) {
      throw new Error('Admin operations require SUPABASE_SERVICE_ROLE_KEY');
    }

    try {
      const auditData: Omit<DbVersionAuditLog, 'id' | 'performed_at'> = {
        tool_id: request.toolId,
        version_id: request.versionId,
        action: request.action,
        performed_by: request.performedBy,
        action_details: request.actionDetails || null,
        ip_address: request.ipAddress,
        user_agent: request.userAgent,
        target_version_number: request.targetVersionNumber,
        rollback_reason: request.rollbackReason,
        session_id: request.sessionId,
        request_id: request.requestId
      };

      const { data, error } = await supabaseAdmin
        .from('version_audit_log')
        .insert([auditData])
        .select('id')
        .single();

      if (error) {
        console.error('Failed to log audit action:', error);
        throw new Error(`Audit logging failed: ${error.message}`);
      }

      return data.id;

    } catch (error) {
      console.error('Audit logging error:', error);
      // Don't throw here to avoid breaking the main operation
      // Log to console and return empty ID
      return '';
    }
  }

  /**
   * Get audit log for a specific tool
   */
  async getAuditLog(
    toolId: string, 
    options: {
      page?: number;
      limit?: number;
      action?: VersionAuditAction;
      performedBy?: string;
      dateFrom?: string;
      dateTo?: string;
    } = {}
  ): Promise<{
    logs: VersionAuditLog[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  }> {
    if (!supabaseAdmin) {
      throw new Error('Admin operations require SUPABASE_SERVICE_ROLE_KEY');
    }

    const page = options.page || 1;
    const limit = options.limit || 50;
    const offset = (page - 1) * limit;

    let query = supabaseAdmin
      .from('version_audit_log')
      .select('*', { count: 'exact' })
      .eq('tool_id', toolId)
      .order('performed_at', { ascending: false });

    // Apply filters
    if (options.action) {
      query = query.eq('action', options.action);
    }
    if (options.performedBy) {
      query = query.eq('performed_by', options.performedBy);
    }
    if (options.dateFrom) {
      query = query.gte('performed_at', options.dateFrom);
    }
    if (options.dateTo) {
      query = query.lte('performed_at', options.dateTo);
    }

    // Apply pagination
    query = query.range(offset, offset + limit - 1);

    const { data: logs, error, count } = await query;

    if (error) {
      throw new Error(`Failed to fetch audit log: ${error.message}`);
    }

    const transformedLogs = logs?.map(log => this.transformDbAuditLogToAuditLog(log)) || [];

    return {
      logs: transformedLogs,
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit)
      }
    };
  }

  /**
   * Get audit statistics for a tool
   */
  async getAuditStatistics(toolId: string): Promise<{
    totalActions: number;
    actionBreakdown: Record<VersionAuditAction, number>;
    topUsers: Array<{
      userId: string;
      actionCount: number;
      lastAction: string;
    }>;
    recentActivity: VersionAuditLog[];
    actionsThisWeek: number;
    actionsThisMonth: number;
  }> {
    if (!supabaseAdmin) {
      throw new Error('Admin operations require SUPABASE_SERVICE_ROLE_KEY');
    }

    const { data: logs, error } = await supabaseAdmin
      .from('version_audit_log')
      .select('*')
      .eq('tool_id', toolId)
      .order('performed_at', { ascending: false });

    if (error) {
      throw new Error(`Failed to fetch audit statistics: ${error.message}`);
    }

    const totalActions = logs?.length || 0;

    // Calculate action breakdown
    const actionBreakdown = logs?.reduce((acc, log) => {
      acc[log.action] = (acc[log.action] || 0) + 1;
      return acc;
    }, {} as Record<VersionAuditAction, number>) || {} as Record<VersionAuditAction, number>;

    // Calculate user activity
    const userCounts = logs?.reduce((acc, log) => {
      acc[log.performed_by] = (acc[log.performed_by] || 0) + 1;
      return acc;
    }, {} as Record<string, number>) || {};

    const topUsers = Object.entries(userCounts)
      .map(([userId, count]) => ({
        userId,
        actionCount: count,
        lastAction: logs?.find(log => log.performed_by === userId)?.performed_at || ''
      }))
      .sort((a, b) => b.actionCount - a.actionCount)
      .slice(0, 5);

    // Get recent activity
    const recentActivity = logs?.slice(0, 10).map(log => this.transformDbAuditLogToAuditLog(log)) || [];

    // Calculate time-based statistics
    const now = new Date();
    const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

    const actionsThisWeek = logs?.filter(log => 
      new Date(log.performed_at) >= oneWeekAgo
    ).length || 0;

    const actionsThisMonth = logs?.filter(log => 
      new Date(log.performed_at) >= oneMonthAgo
    ).length || 0;

    return {
      totalActions,
      actionBreakdown,
      topUsers,
      recentActivity,
      actionsThisWeek,
      actionsThisMonth
    };
  }

  /**
   * Clean up old audit logs
   */
  async cleanupOldLogs(retentionDays: number = 90): Promise<number> {
    if (!supabaseAdmin) {
      throw new Error('Admin operations require SUPABASE_SERVICE_ROLE_KEY');
    }

    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

    const { data, error } = await supabaseAdmin
      .from('version_audit_log')
      .delete()
      .lt('performed_at', cutoffDate.toISOString())
      .select('id');

    if (error) {
      throw new Error(`Failed to cleanup audit logs: ${error.message}`);
    }

    return data?.length || 0;
  }

  /**
   * Search audit logs
   */
  async searchAuditLogs(
    searchTerm: string,
    options: {
      toolId?: string;
      action?: VersionAuditAction;
      performedBy?: string;
      limit?: number;
    } = {}
  ): Promise<VersionAuditLog[]> {
    if (!supabaseAdmin) {
      throw new Error('Admin operations require SUPABASE_SERVICE_ROLE_KEY');
    }

    let query = supabaseAdmin
      .from('version_audit_log')
      .select('*')
      .or(`rollback_reason.ilike.%${searchTerm}%,action_details::text.ilike.%${searchTerm}%`)
      .order('performed_at', { ascending: false })
      .limit(options.limit || 100);

    // Apply filters
    if (options.toolId) {
      query = query.eq('tool_id', options.toolId);
    }
    if (options.action) {
      query = query.eq('action', options.action);
    }
    if (options.performedBy) {
      query = query.eq('performed_by', options.performedBy);
    }

    const { data: logs, error } = await query;

    if (error) {
      throw new Error(`Failed to search audit logs: ${error.message}`);
    }

    return logs?.map(log => this.transformDbAuditLogToAuditLog(log)) || [];
  }

  /**
   * Transform database audit log to application format
   */
  private transformDbAuditLogToAuditLog(dbLog: DbVersionAuditLog): VersionAuditLog {
    return {
      id: dbLog.id,
      toolId: dbLog.tool_id,
      versionId: dbLog.version_id,
      action: dbLog.action,
      performedBy: dbLog.performed_by,
      performedAt: dbLog.performed_at,
      actionDetails: dbLog.action_details,
      ipAddress: dbLog.ip_address,
      userAgent: dbLog.user_agent,
      targetVersionNumber: dbLog.target_version_number,
      rollbackReason: dbLog.rollback_reason,
      sessionId: dbLog.session_id,
      requestId: dbLog.request_id
    };
  }
}
