'use client';

import { useState, useEffect } from 'react';
import { BarChart3, TrendingUp, Users, Clock, GitBranch, RotateCcw } from 'lucide-react';
import { VersionStatistics as VersionStatsType } from '@/lib/types/versioning';

interface VersionStatisticsProps {
  toolId: string;
}

export function VersionStatistics({ toolId }: VersionStatisticsProps) {
  const [statistics, setStatistics] = useState<VersionStatsType | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchStatistics();
  }, [toolId]);

  const fetchStatistics = async () => {
    try {
      setLoading(true);
      setError(null);

      const adminApiKey = process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789';
      
      const response = await fetch(`/api/admin/tools/${toolId}/versions/statistics?includeAudit=true`, {
        headers: {
          'x-api-key': adminApiKey,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch statistics');
      }

      const result = await response.json();
      
      if (result.success) {
        setStatistics(result.data.versionStatistics);
      } else {
        throw new Error(result.error || 'Failed to fetch statistics');
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch statistics');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="bg-zinc-800 rounded-lg p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-zinc-700 rounded mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {[...Array(8)].map((_, i) => (
              <div key={i} className="h-24 bg-zinc-700 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-zinc-800 rounded-lg p-6">
        <div className="text-red-400 text-center">
          <p className="mb-4">Error loading statistics: {error}</p>
          <button
            onClick={fetchStatistics}
            className="px-4 py-2 bg-red-600 hover:bg-red-700 rounded-lg transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  if (!statistics) {
    return (
      <div className="bg-zinc-800 rounded-lg p-6">
        <div className="text-center text-gray-400 py-8">
          <BarChart3 className="w-12 h-12 mx-auto mb-4 opacity-50" />
          <p>No statistics available</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-zinc-800 rounded-lg p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-600 rounded-full">
              <GitBranch className="w-4 h-4 text-white" />
            </div>
            <div>
              <p className="text-sm text-gray-400">Total Versions</p>
              <p className="text-2xl font-bold text-white">{statistics.totalVersions}</p>
            </div>
          </div>
        </div>

        <div className="bg-zinc-800 rounded-lg p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-green-600 rounded-full">
              <TrendingUp className="w-4 h-4 text-white" />
            </div>
            <div>
              <p className="text-sm text-gray-400">Current Version</p>
              <p className="text-2xl font-bold text-white">{statistics.currentVersion}</p>
            </div>
          </div>
        </div>

        <div className="bg-zinc-800 rounded-lg p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-orange-600 rounded-full">
              <Clock className="w-4 h-4 text-white" />
            </div>
            <div>
              <p className="text-sm text-gray-400">This Week</p>
              <p className="text-2xl font-bold text-white">{statistics.versionsThisWeek}</p>
            </div>
          </div>
        </div>

        <div className="bg-zinc-800 rounded-lg p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-purple-600 rounded-full">
              <BarChart3 className="w-4 h-4 text-white" />
            </div>
            <div>
              <p className="text-sm text-gray-400">This Month</p>
              <p className="text-2xl font-bold text-white">{statistics.versionsThisMonth}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Change Type Breakdown */}
      <div className="bg-zinc-800 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-white mb-4">Change Type Breakdown</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {Object.entries(statistics.changeTypeBreakdown).map(([type, count]) => (
            <div key={type} className="text-center">
              <div className="text-xl font-bold text-white">{count}</div>
              <div className="text-sm text-gray-400 capitalize">{type.replace('_', ' ')}</div>
            </div>
          ))}
        </div>
      </div>

      {/* Change Source Breakdown */}
      <div className="bg-zinc-800 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-white mb-4">Change Source Breakdown</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {Object.entries(statistics.changeSourceBreakdown).map(([source, count]) => (
            <div key={source} className="text-center">
              <div className="text-xl font-bold text-white">{count}</div>
              <div className="text-sm text-gray-400 capitalize">{source.replace('_', ' ')}</div>
            </div>
          ))}
        </div>
      </div>

      {/* Top Contributors */}
      <div className="bg-zinc-800 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-white mb-4">Top Contributors</h3>
        {statistics.topContributors.length > 0 ? (
          <div className="space-y-3">
            {statistics.topContributors.map((contributor, index) => (
              <div key={contributor.userId} className="flex items-center justify-between p-3 bg-zinc-700 rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white font-medium">
                    {index + 1}
                  </div>
                  <div>
                    <p className="font-medium text-white">{contributor.userId}</p>
                    <p className="text-sm text-gray-400">
                      Last change: {new Date(contributor.lastChange).toLocaleDateString()}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-medium text-white">{contributor.versionCount}</p>
                  <p className="text-sm text-gray-400">versions</p>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <p className="text-gray-400 text-center py-4">No contributors data available</p>
        )}
      </div>

      {/* Recent Versions */}
      <div className="bg-zinc-800 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-white mb-4">Recent Versions</h3>
        {statistics.recentVersions.length > 0 ? (
          <div className="space-y-3">
            {statistics.recentVersions.map((version) => (
              <div key={version.id} className="flex items-center justify-between p-3 bg-zinc-700 rounded-lg">
                <div className="flex items-center gap-3">
                  <div className={`p-2 rounded-full ${
                    version.changeType === 'create' ? 'bg-green-600' :
                    version.changeType === 'update' ? 'bg-blue-600' :
                    version.changeType === 'rollback' ? 'bg-orange-600' :
                    'bg-purple-600'
                  }`}>
                    {version.changeType === 'rollback' ? (
                      <RotateCcw className="w-3 h-3 text-white" />
                    ) : (
                      <GitBranch className="w-3 h-3 text-white" />
                    )}
                  </div>
                  <div>
                    <p className="font-medium text-white">Version {version.versionNumber}</p>
                    <p className="text-sm text-gray-400">
                      {version.changeSummary || 'No description'}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm text-white">{version.createdBy}</p>
                  <p className="text-xs text-gray-400">
                    {new Date(version.createdAt).toLocaleDateString()}
                  </p>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <p className="text-gray-400 text-center py-4">No recent versions</p>
        )}
      </div>

      {/* Last Rollback */}
      {statistics.lastRollback && (
        <div className="bg-orange-500/10 border border-orange-500/20 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-orange-300 mb-4 flex items-center gap-2">
            <RotateCcw className="w-5 h-5" />
            Last Rollback
          </h3>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-gray-400">Version:</span>
              <span className="text-white">{statistics.lastRollback.versionNumber}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Performed by:</span>
              <span className="text-white">{statistics.lastRollback.performedBy}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Date:</span>
              <span className="text-white">
                {new Date(statistics.lastRollback.performedAt).toLocaleString()}
              </span>
            </div>
            <div>
              <span className="text-gray-400">Reason:</span>
              <p className="text-white mt-1">{statistics.lastRollback.reason}</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
