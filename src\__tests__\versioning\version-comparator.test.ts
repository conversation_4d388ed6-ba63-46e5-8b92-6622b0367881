/**
 * @jest-environment node
 */

import { VersionComparator } from '@/lib/versioning/version-comparator';
import { CompareVersionsRequest } from '@/lib/types/versioning';
import { AITool } from '@/lib/types';

// Mock Supabase
jest.mock('@/lib/supabase', () => ({
  supabaseAdmin: {
    from: jest.fn(() => ({
      select: jest.fn(() => ({
        eq: jest.fn(() => ({
          single: jest.fn(),
          order: jest.fn(() => ({
            limit: jest.fn()
          })),
          gt: jest.fn(() => ({
            or: jest.fn(() => ({
              single: jest.fn()
            }))
          })),
          lt: jest.fn(() => ({
            select: jest.fn()
          }))
        })),
        insert: jest.fn(),
        delete: jest.fn(() => ({
          lt: jest.fn(() => ({
            select: jest.fn()
          }))
        }))
      }))
    }))
  }
}));

describe('VersionComparator', () => {
  let versionComparator: VersionComparator;
  let mockSupabase: any;

  beforeEach(() => {
    versionComparator = new VersionComparator();
    mockSupabase = require('@/lib/supabase').supabaseAdmin;
    jest.clearAllMocks();
  });

  describe('compareVersions', () => {
    it('should compare two versions and return differences', async () => {
      const fromVersionData: AITool = {
        id: 'test-tool',
        name: 'Old Tool Name',
        slug: 'old-tool',
        description: 'Old description',
        link: '/tools/old-tool',
        isVerified: false,
        pricing: {
          type: 'free',
          plans: []
        }
      };

      const toVersionData: AITool = {
        id: 'test-tool',
        name: 'New Tool Name',
        slug: 'new-tool',
        description: 'New description',
        link: '/tools/new-tool',
        isVerified: true,
        pricing: {
          type: 'paid',
          plans: [{ name: 'Basic', price: '$10/month' }]
        }
      };

      const mockFromVersion = {
        id: 'from-version-id',
        tool_id: 'test-tool',
        version_number: 1,
        version_data: fromVersionData
      };

      const mockToVersion = {
        id: 'to-version-id',
        tool_id: 'test-tool',
        version_number: 2,
        version_data: toVersionData
      };

      // Mock cache miss
      mockSupabase.from.mockReturnValueOnce({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            gt: jest.fn().mockReturnValue({
              or: jest.fn().mockReturnValue({
                single: jest.fn().mockResolvedValue({
                  data: null,
                  error: { message: 'Not found' }
                })
              })
            })
          })
        })
      });

      // Mock version data fetches
      mockSupabase.from
        .mockReturnValueOnce({
          select: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              single: jest.fn().mockResolvedValue({
                data: mockFromVersion,
                error: null
              })
            })
          })
        })
        .mockReturnValueOnce({
          select: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              single: jest.fn().mockResolvedValue({
                data: mockToVersion,
                error: null
              })
            })
          })
        });

      // Mock cache insert
      mockSupabase.from.mockReturnValueOnce({
        insert: jest.fn().mockResolvedValue({
          data: null,
          error: null
        })
      });

      const request: CompareVersionsRequest = {
        toolId: 'test-tool',
        fromVersionNumber: 1,
        toVersionNumber: 2,
        includeMetadata: true
      };

      const result = await versionComparator.compareVersions(request);

      expect(result.summary.totalChanges).toBeGreaterThan(0);
      expect(result.changes).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            field: 'name',
            type: 'modified',
            oldValue: 'Old Tool Name',
            newValue: 'New Tool Name'
          }),
          expect.objectContaining({
            field: 'isVerified',
            type: 'modified',
            oldValue: false,
            newValue: true
          })
        ])
      );
      expect(result.metadata).toBeDefined();
      expect(result.metadata.fromVersion).toBe(1);
      expect(result.metadata.toVersion).toBe(2);
    });

    it('should return cached comparison if available', async () => {
      const cachedComparison = {
        summary: {
          totalChanges: 1,
          addedFields: 0,
          modifiedFields: 1,
          removedFields: 0,
          significantChanges: ['Modified name']
        },
        changes: [
          {
            field: 'name',
            type: 'modified' as const,
            oldValue: 'Old Name',
            newValue: 'New Name',
            path: 'name',
            significance: 'high' as const
          }
        ],
        metadata: {
          comparedAt: '2024-01-01T00:00:00Z',
          fromVersion: 1,
          toVersion: 2,
          algorithm: 'deep-object-diff',
          processingTime: 100
        }
      };

      const mockCachedData = {
        id: 'cache-id',
        tool_id: 'test-tool',
        from_version_id: 'from-version-id',
        to_version_id: 'to-version-id',
        comparison_data: cachedComparison,
        created_at: '2024-01-01T00:00:00Z',
        expires_at: '2024-01-02T00:00:00Z'
      };

      // Mock cache hit
      mockSupabase.from.mockReturnValueOnce({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            gt: jest.fn().mockReturnValue({
              or: jest.fn().mockReturnValue({
                single: jest.fn().mockResolvedValue({
                  data: mockCachedData,
                  error: null
                })
              })
            })
          })
        })
      });

      const request: CompareVersionsRequest = {
        toolId: 'test-tool',
        fromVersionNumber: 1,
        toVersionNumber: 2
      };

      const result = await versionComparator.compareVersions(request);

      expect(result).toEqual(cachedComparison);
    });

    it('should handle version not found error', async () => {
      // Mock cache miss
      mockSupabase.from.mockReturnValueOnce({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            gt: jest.fn().mockReturnValue({
              or: jest.fn().mockReturnValue({
                single: jest.fn().mockResolvedValue({
                  data: null,
                  error: { message: 'Not found' }
                })
              })
            })
          })
        })
      });

      // Mock version not found
      mockSupabase.from.mockReturnValueOnce({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: null,
              error: { message: 'Version not found' }
            })
          })
        })
      });

      const request: CompareVersionsRequest = {
        toolId: 'test-tool',
        fromVersionNumber: 1,
        toVersionNumber: 999
      };

      await expect(versionComparator.compareVersions(request)).rejects.toThrow('Comparison failed');
    });
  });

  describe('generateChangeSummary', () => {
    it('should generate human-readable summary for no changes', () => {
      const diff = {
        summary: {
          totalChanges: 0,
          addedFields: 0,
          modifiedFields: 0,
          removedFields: 0,
          significantChanges: []
        },
        changes: [],
        metadata: {
          comparedAt: '2024-01-01T00:00:00Z',
          fromVersion: 1,
          toVersion: 2,
          algorithm: 'deep-object-diff',
          processingTime: 50
        }
      };

      const summary = versionComparator.generateChangeSummary(diff);
      expect(summary).toBe('No changes detected between versions');
    });

    it('should generate summary for mixed changes', () => {
      const diff = {
        summary: {
          totalChanges: 5,
          addedFields: 2,
          modifiedFields: 2,
          removedFields: 1,
          significantChanges: ['Modified name', 'Added pricing']
        },
        changes: [],
        metadata: {
          comparedAt: '2024-01-01T00:00:00Z',
          fromVersion: 1,
          toVersion: 2,
          algorithm: 'deep-object-diff',
          processingTime: 150
        }
      };

      const summary = versionComparator.generateChangeSummary(diff);
      expect(summary).toContain('2 fields added');
      expect(summary).toContain('2 fields modified');
      expect(summary).toContain('1 field removed');
      expect(summary).toContain('Significant changes: Modified name, Added pricing');
    });

    it('should handle singular vs plural correctly', () => {
      const diff = {
        summary: {
          totalChanges: 1,
          addedFields: 1,
          modifiedFields: 0,
          removedFields: 0,
          significantChanges: []
        },
        changes: [],
        metadata: {
          comparedAt: '2024-01-01T00:00:00Z',
          fromVersion: 1,
          toVersion: 2,
          algorithm: 'deep-object-diff',
          processingTime: 25
        }
      };

      const summary = versionComparator.generateChangeSummary(diff);
      expect(summary).toBe('1 field added');
    });
  });

  describe('cleanupExpiredComparisons', () => {
    it('should clean up expired comparisons', async () => {
      const mockDeletedComparisons = [
        { id: 'expired-1' },
        { id: 'expired-2' }
      ];

      mockSupabase.from.mockReturnValueOnce({
        delete: jest.fn().mockReturnValue({
          lt: jest.fn().mockReturnValue({
            select: jest.fn().mockResolvedValue({
              data: mockDeletedComparisons,
              error: null
            })
          })
        })
      });

      const result = await versionComparator.cleanupExpiredComparisons();

      expect(result).toBe(2);
    });

    it('should handle cleanup errors gracefully', async () => {
      mockSupabase.from.mockReturnValueOnce({
        delete: jest.fn().mockReturnValue({
          lt: jest.fn().mockReturnValue({
            select: jest.fn().mockResolvedValue({
              data: null,
              error: { message: 'Cleanup failed' }
            })
          })
        })
      });

      await expect(versionComparator.cleanupExpiredComparisons()).rejects.toThrow('Failed to cleanup expired comparisons');
    });
  });

  describe('getComparisonHistory', () => {
    it('should fetch comparison history for a tool', async () => {
      const mockComparisons = [
        {
          id: 'comp-1',
          tool_id: 'test-tool',
          from_version_id: 'v1',
          to_version_id: 'v2',
          comparison_data: {},
          created_at: '2024-01-01T00:00:00Z',
          expires_at: '2024-01-02T00:00:00Z'
        }
      ];

      mockSupabase.from.mockReturnValueOnce({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            order: jest.fn().mockReturnValue({
              limit: jest.fn().mockResolvedValue({
                data: mockComparisons,
                error: null
              })
            })
          })
        })
      });

      const result = await versionComparator.getComparisonHistory('test-tool', 10);

      expect(result).toHaveLength(1);
      expect(result[0].toolId).toBe('test-tool');
    });
  });

  describe('performDeepComparison', () => {
    it('should detect added fields', () => {
      const fromData = { name: 'Test' };
      const toData = { name: 'Test', description: 'New field' };

      // Access private method for testing
      const comparison = (versionComparator as any).performDeepComparison(fromData, toData, 1, 2);

      expect(comparison.summary.addedFields).toBe(1);
      expect(comparison.changes).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            field: 'description',
            type: 'added',
            newValue: 'New field'
          })
        ])
      );
    });

    it('should detect removed fields', () => {
      const fromData = { name: 'Test', description: 'Old field' };
      const toData = { name: 'Test' };

      const comparison = (versionComparator as any).performDeepComparison(fromData, toData, 1, 2);

      expect(comparison.summary.removedFields).toBe(1);
      expect(comparison.changes).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            field: 'description',
            type: 'removed',
            oldValue: 'Old field'
          })
        ])
      );
    });

    it('should detect modified fields', () => {
      const fromData = { name: 'Old Name' };
      const toData = { name: 'New Name' };

      const comparison = (versionComparator as any).performDeepComparison(fromData, toData, 1, 2);

      expect(comparison.summary.modifiedFields).toBe(1);
      expect(comparison.changes).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            field: 'name',
            type: 'modified',
            oldValue: 'Old Name',
            newValue: 'New Name'
          })
        ])
      );
    });

    it('should handle nested object changes', () => {
      const fromData = {
        pricing: {
          type: 'free',
          plans: []
        }
      };
      const toData = {
        pricing: {
          type: 'paid',
          plans: [{ name: 'Basic' }]
        }
      };

      const comparison = (versionComparator as any).performDeepComparison(fromData, toData, 1, 2);

      expect(comparison.changes.length).toBeGreaterThan(0);
      expect(comparison.changes).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            path: 'pricing.type',
            type: 'modified'
          })
        ])
      );
    });
  });
});
