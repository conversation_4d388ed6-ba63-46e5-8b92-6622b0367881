'use client';

import { useState } from 'react';
import { GitBranch, History, BarChart3 } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { ToolVersion } from '@/lib/types/versioning';
import { VersionHistory } from './VersionHistory';
import { RollbackDialog } from './RollbackDialog';
import { VersionComparison } from './VersionComparison';
import { VersionStatistics } from './VersionStatistics';

interface VersionManagementProps {
  toolId: string;
  toolName: string;
}

type ActiveTab = 'history' | 'statistics';

export function VersionManagement({ toolId, toolName }: VersionManagementProps) {
  const [activeTab, setActiveTab] = useState<ActiveTab>('history');
  const [rollbackDialog, setRollbackDialog] = useState<{
    isOpen: boolean;
    version: ToolVersion | null;
  }>({
    isOpen: false,
    version: null
  });
  const [comparisonDialog, setComparisonDialog] = useState<{
    isOpen: boolean;
    fromVersion: number;
    toVersion: number;
  }>({
    isOpen: false,
    fromVersion: 0,
    toVersion: 0
  });
  const [isRollingBack, setIsRollingBack] = useState(false);
  const [rollbackSuccess, setRollbackSuccess] = useState<string | null>(null);
  const [rollbackError, setRollbackError] = useState<string | null>(null);

  const handleRollback = (version: ToolVersion) => {
    setRollbackDialog({
      isOpen: true,
      version
    });
  };

  const handleCompare = (fromVersion: number, toVersion: number) => {
    setComparisonDialog({
      isOpen: true,
      fromVersion,
      toVersion
    });
  };

  const handleConfirmRollback = async (reason: string) => {
    if (!rollbackDialog.version) return;

    setIsRollingBack(true);
    setRollbackError(null);

    try {
      const adminApiKey = process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789';
      
      const response = await fetch(`/api/admin/tools/${toolId}/versions/rollback`, {
        method: 'POST',
        headers: {
          'x-api-key': adminApiKey,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          targetVersionNumber: rollbackDialog.version.versionNumber,
          reason,
          performedBy: 'admin', // This should come from user context
          sessionId: `session_${Date.now()}`,
          requestId: `rollback_${Date.now()}`
        })
      });

      if (!response.ok) {
        throw new Error('Failed to perform rollback');
      }

      const result = await response.json();
      
      if (result.success) {
        setRollbackSuccess(
          `Successfully rolled back to version ${rollbackDialog.version.versionNumber}`
        );
        
        // Close dialog and refresh data
        setRollbackDialog({ isOpen: false, version: null });
        
        // Clear success message after 5 seconds
        setTimeout(() => setRollbackSuccess(null), 5000);
        
      } else {
        throw new Error(result.error || 'Rollback failed');
      }

    } catch (err) {
      setRollbackError(err instanceof Error ? err.message : 'Rollback failed');
    } finally {
      setIsRollingBack(false);
    }
  };

  const tabs = [
    {
      id: 'history' as const,
      label: 'Version History',
      icon: History,
      description: 'View and manage tool versions'
    },
    {
      id: 'statistics' as const,
      label: 'Statistics',
      icon: BarChart3,
      description: 'Version analytics and insights'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-blue-600 rounded-full">
            <GitBranch className="w-5 h-5 text-white" />
          </div>
          <div>
            <h2 className="text-xl font-semibold text-white">Version Management</h2>
            <p className="text-sm text-gray-400">
              Manage versions for "{toolName}"
            </p>
          </div>
        </div>
      </div>

      {/* Success/Error Messages */}
      {rollbackSuccess && (
        <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-4">
          <p className="text-green-300">{rollbackSuccess}</p>
        </div>
      )}

      {rollbackError && (
        <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4">
          <p className="text-red-300">Error: {rollbackError}</p>
          <Button
            onClick={() => setRollbackError(null)}
            variant="outline"
            size="sm"
            className="mt-2"
          >
            Dismiss
          </Button>
        </div>
      )}

      {/* Tab Navigation */}
      <div className="border-b border-zinc-700">
        <nav className="flex space-x-8">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center gap-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === tab.id
                    ? 'border-orange-500 text-orange-400'
                    : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'
                }`}
              >
                <Icon className="w-4 h-4" />
                {tab.label}
              </button>
            );
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="min-h-[600px]">
        {activeTab === 'history' && (
          <VersionHistory
            toolId={toolId}
            onRollback={handleRollback}
            onCompare={handleCompare}
            onViewVersion={(version) => {
              // Could implement a view version dialog here
              console.log('View version:', version);
            }}
          />
        )}

        {activeTab === 'statistics' && (
          <VersionStatistics toolId={toolId} />
        )}
      </div>

      {/* Dialogs */}
      <RollbackDialog
        isOpen={rollbackDialog.isOpen}
        onClose={() => setRollbackDialog({ isOpen: false, version: null })}
        onConfirm={handleConfirmRollback}
        version={rollbackDialog.version}
        toolId={toolId}
      />

      <VersionComparison
        isOpen={comparisonDialog.isOpen}
        onClose={() => setComparisonDialog({ isOpen: false, fromVersion: 0, toVersion: 0 })}
        toolId={toolId}
        fromVersion={comparisonDialog.fromVersion}
        toVersion={comparisonDialog.toVersion}
      />
    </div>
  );
}
