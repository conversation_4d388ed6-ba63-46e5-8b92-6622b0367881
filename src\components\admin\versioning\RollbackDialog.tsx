'use client';

import { useState, useEffect } from 'react';
import { <PERSON>ert<PERSON>riangle, RotateCcw, X, CheckCircle, XCircle } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { ToolVersion, RollbackValidation } from '@/lib/types/versioning';

interface RollbackDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (reason: string) => Promise<void>;
  version: ToolVersion | null;
  toolId: string;
}

export function RollbackDialog({ 
  isOpen, 
  onClose, 
  onConfirm, 
  version, 
  toolId 
}: RollbackDialogProps) {
  const [reason, setReason] = useState('');
  const [validation, setValidation] = useState<RollbackValidation | null>(null);
  const [isValidating, setIsValidating] = useState(false);
  const [isRollingBack, setIsRollingBack] = useState(false);
  const [validationError, setValidationError] = useState<string | null>(null);

  useEffect(() => {
    if (isOpen && version) {
      validateRollback();
    } else {
      setValidation(null);
      setValidationError(null);
      setReason('');
    }
  }, [isOpen, version, toolId]);

  const validateRollback = async () => {
    if (!version) return;

    setIsValidating(true);
    setValidationError(null);

    try {
      const adminApiKey = process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789';
      
      const response = await fetch(
        `/api/admin/tools/${toolId}/versions/rollback/validate?targetVersion=${version.versionNumber}`,
        {
          headers: {
            'x-api-key': adminApiKey,
            'Content-Type': 'application/json'
          }
        }
      );

      if (!response.ok) {
        throw new Error('Failed to validate rollback');
      }

      const result = await response.json();
      
      if (result.success) {
        setValidation(result.data);
      } else {
        throw new Error(result.error || 'Validation failed');
      }

    } catch (err) {
      setValidationError(err instanceof Error ? err.message : 'Validation failed');
    } finally {
      setIsValidating(false);
    }
  };

  const handleConfirm = async () => {
    if (!reason.trim() || !validation?.canRollback) return;

    setIsRollingBack(true);
    try {
      await onConfirm(reason.trim());
      onClose();
    } catch (error) {
      // Error handling is done in parent component
    } finally {
      setIsRollingBack(false);
    }
  };

  if (!isOpen || !version) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-zinc-800 rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-zinc-700">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-orange-600 rounded-full">
              <RotateCcw className="w-5 h-5 text-white" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-white">
                Rollback to Version {version.versionNumber}
              </h2>
              <p className="text-sm text-gray-400">
                This action will revert the tool to a previous state
              </p>
            </div>
          </div>
          <Button
            onClick={onClose}
            variant="outline"
            size="sm"
            className="text-gray-400 hover:text-white"
          >
            <X className="w-4 h-4" />
          </Button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Version Information */}
          <div className="bg-zinc-700 rounded-lg p-4">
            <h3 className="font-medium text-white mb-3">Target Version Details</h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-400">Version:</span>
                <span className="text-white">{version.versionNumber}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Created:</span>
                <span className="text-white">{new Date(version.createdAt).toLocaleString()}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Created by:</span>
                <span className="text-white">{version.createdBy}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Change type:</span>
                <span className="text-white capitalize">{version.changeType.replace('_', ' ')}</span>
              </div>
              {version.changeSummary && (
                <div>
                  <span className="text-gray-400">Description:</span>
                  <p className="text-white mt-1">{version.changeSummary}</p>
                </div>
              )}
            </div>
          </div>

          {/* Validation Status */}
          {isValidating ? (
            <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
              <div className="flex items-center gap-3">
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-400"></div>
                <span className="text-blue-300">Validating rollback...</span>
              </div>
            </div>
          ) : validationError ? (
            <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4">
              <div className="flex items-center gap-3">
                <XCircle className="w-5 h-5 text-red-400" />
                <div>
                  <p className="text-red-300 font-medium">Validation Error</p>
                  <p className="text-red-400 text-sm mt-1">{validationError}</p>
                </div>
              </div>
              <Button
                onClick={validateRollback}
                variant="outline"
                size="sm"
                className="mt-3"
              >
                Retry Validation
              </Button>
            </div>
          ) : validation ? (
            <div className={`border rounded-lg p-4 ${
              validation.canRollback 
                ? 'bg-green-500/10 border-green-500/20' 
                : 'bg-red-500/10 border-red-500/20'
            }`}>
              <div className="flex items-center gap-3 mb-3">
                {validation.canRollback ? (
                  <CheckCircle className="w-5 h-5 text-green-400" />
                ) : (
                  <XCircle className="w-5 h-5 text-red-400" />
                )}
                <span className={`font-medium ${
                  validation.canRollback ? 'text-green-300' : 'text-red-300'
                }`}>
                  {validation.canRollback ? 'Rollback Validated' : 'Rollback Not Possible'}
                </span>
              </div>

              {/* Errors */}
              {validation.errors.length > 0 && (
                <div className="mb-3">
                  <p className="text-red-300 font-medium text-sm mb-2">Errors:</p>
                  <ul className="list-disc list-inside space-y-1">
                    {validation.errors.map((error, index) => (
                      <li key={index} className="text-red-400 text-sm">{error}</li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Warnings */}
              {validation.warnings.length > 0 && (
                <div className="mb-3">
                  <p className="text-orange-300 font-medium text-sm mb-2">Warnings:</p>
                  <ul className="list-disc list-inside space-y-1">
                    {validation.warnings.map((warning, index) => (
                      <li key={index} className="text-orange-400 text-sm">{warning}</li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Impact Analysis */}
              {validation.impactAnalysis && (
                <div className="bg-zinc-800 rounded p-3">
                  <p className="text-white font-medium text-sm mb-2">Impact Analysis:</p>
                  <div className="grid grid-cols-2 gap-4 text-xs">
                    <div>
                      <span className="text-gray-400">Data Loss Risk:</span>
                      <span className={`ml-2 capitalize ${
                        validation.impactAnalysis.dataLossRisk === 'high' ? 'text-red-400' :
                        validation.impactAnalysis.dataLossRisk === 'medium' ? 'text-orange-400' :
                        'text-green-400'
                      }`}>
                        {validation.impactAnalysis.dataLossRisk}
                      </span>
                    </div>
                    <div>
                      <span className="text-gray-400">Estimated Downtime:</span>
                      <span className="text-white ml-2">
                        {validation.impactAnalysis.estimatedDowntime}s
                      </span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          ) : null}

          {/* Reason Input */}
          {validation?.canRollback && (
            <div>
              <label className="block text-sm font-medium text-white mb-2">
                Rollback Reason <span className="text-red-400">*</span>
              </label>
              <textarea
                value={reason}
                onChange={(e) => setReason(e.target.value)}
                placeholder="Please provide a reason for this rollback..."
                className="w-full px-3 py-2 bg-zinc-700 border border-zinc-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent resize-none"
                rows={3}
                maxLength={500}
              />
              <div className="flex justify-between mt-1">
                <p className="text-xs text-gray-400">
                  Explain why this rollback is necessary
                </p>
                <span className="text-xs text-gray-400">
                  {reason.length}/500
                </span>
              </div>
            </div>
          )}

          {/* Warning */}
          {validation?.canRollback && (
            <div className="bg-orange-500/10 border border-orange-500/20 rounded-lg p-4">
              <div className="flex items-start gap-3">
                <AlertTriangle className="w-5 h-5 text-orange-400 mt-0.5" />
                <div>
                  <p className="text-orange-300 font-medium">Important Notice</p>
                  <p className="text-orange-400 text-sm mt-1">
                    This action will permanently change the tool's current state. 
                    A new version will be created to track this rollback operation.
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end gap-3 p-6 border-t border-zinc-700">
          <Button
            onClick={onClose}
            variant="outline"
            disabled={isRollingBack}
          >
            Cancel
          </Button>
          
          <Button
            onClick={handleConfirm}
            disabled={!validation?.canRollback || !reason.trim() || isRollingBack}
            className="bg-orange-600 hover:bg-orange-700"
          >
            {isRollingBack ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Rolling Back...
              </>
            ) : (
              <>
                <RotateCcw className="w-4 h-4 mr-2" />
                Confirm Rollback
              </>
            )}
          </Button>
        </div>
      </div>
    </div>
  );
}
